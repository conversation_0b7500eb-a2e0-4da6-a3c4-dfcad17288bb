import fs from "fs/promises";
import path from "path";

const LANG_DIR = "./langs";
const OUTPUT = "./src/Lang.java";

function escapeJava(str) {
  return JSON.stringify(str);
}

function flattenObject(obj, prefix = "") {
  const flattened = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      if (
        typeof obj[key] === "object" &&
        obj[key] !== null &&
        !Array.isArray(obj[key])
      ) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  return flattened;
}

// Hyper-optimized version with static arrays
function generateHyperOptimized(langs, langCodes, allKeys) {
  const b = [];

  b.push("import java.util.Hashtable;");
  b.push("");
  b.push("public class Lang {");
  b.push('  private static String c = "en";');
  b.push("  private static Hashtable d;");
  b.push("  private static boolean i = false;");

  // Pre-calculate all translations as static arrays
  b.push("");
  b.push("  private static final String[] KEYS = {");
  const keyChunks = [];
  for (let i = 0; i < allKeys.length; i += 10) {
    const chunk = allKeys
      .slice(i, i + 10)
      .map((k) => `"${k}"`)
      .join(", ");
    keyChunks.push("    " + chunk + (i + 10 < allKeys.length ? "," : ""));
  }
  b.push(keyChunks.join("\n"));
  b.push("  };");

  // Generate compact translation arrays for each language
  for (const code of langCodes) {
    const map = langs[code];
    const varName = code.toUpperCase() + "_VALS";

    b.push("");
    b.push(`  private static final String[] ${varName} = {`);

    const valueChunks = [];
    for (let i = 0; i < allKeys.length; i += 5) {
      const chunk = allKeys
        .slice(i, i + 5)
        .map((key) => {
          const value = (map[key] ?? "").trim();
          const fallback = langs.en[key] ?? key;
          const finalVal = value.length > 0 ? value : fallback;
          return escapeJava(finalVal);
        })
        .join(", ");
      valueChunks.push("    " + chunk + (i + 5 < allKeys.length ? "," : ""));
    }
    b.push(valueChunks.join("\n"));
    b.push("  };");
  }

  // Ultra-compact load method
  b.push("");
  b.push("  private static void l(String code) {");
  b.push("    if (d == null) {");
  b.push("      d = new Hashtable();");
  b.push("    } else {");
  b.push("      d.clear();");
  b.push("    }");
  b.push("    String[] vals = EN_VALS;"); // default

  for (const code of langCodes) {
    if (code !== "en") {
      b.push(`    if ("${code}".equals(code)) {`);
      b.push(`      vals = ${code.toUpperCase()}_VALS;`);
      b.push("    }");
    }
  }

  b.push("    for (int j = 0; j < KEYS.length; j++) {");
  b.push("      d.put(KEYS[j], vals[j]);");
  b.push("    }");
  b.push("  }");

  // Same public interface
  b.push("");
  b.push("  public static void setLang(String code) {");
  b.push("    if (!code.equals(c)) {");
  b.push("      c = code;");
  b.push("      l(code);");
  b.push("      i = true;");
  b.push("    }");
  b.push("  }");

  b.push("");
  b.push("  public static String getCurrentLang() { return c; }");

  b.push("");
  b.push("  public static String[] getAvailableLanguages() {");
  const langArray = langCodes.map((code) => `"${code}"`).join(", ");
  b.push(`    return new String[] {${langArray}};`);
  b.push("  }");

  b.push("");
  b.push("  public static String tr(String k) {");
  b.push("    if (!i) {");
  b.push("      l(c);");
  b.push("      i = true;");
  b.push("    }");
  b.push("    String v = (String) d.get(k);");
  b.push("    return v != null ? v : k;");
  b.push("  }");

  b.push("");
  b.push("  public static String tr(String k, String a) {");
  b.push('    return Utils.replace(tr(k), "{0}", a);');
  b.push("  }");

  b.push("");
  b.push("  public static String tr(String k, String a, String b) {");
  b.push("    String t = tr(k);");
  b.push('    return Utils.replace(Utils.replace(t, "{0}", a), "{1}", b);');
  b.push("  }");

  b.push("");
  b.push("  private Lang() {}");
  b.push("}");

  return b.join("\n");
}

async function main() {
  const files = await fs.readdir(LANG_DIR);
  const langs = {};
  const langCodes = [];

  for (const file of files) {
    if (!file.endsWith(".json")) {
      continue;
    }
    const code = path.basename(file, ".json");
    const raw = await fs.readFile(path.join(LANG_DIR, file), "utf-8");
    const jsonData = JSON.parse(raw);
    const flattened = flattenObject(jsonData);
    langs[code] = { lang: code, ...flattened };
    langCodes.push(code);
  }

  if (!langs.en) {
    console.error('❌ Missing "en.json" (fallback base)');
    return;
  }

  const allKeys = Object.keys(langs.en);

  const javaCode = generateHyperOptimized(langs, langCodes, allKeys);

  await fs.writeFile(OUTPUT, javaCode, "utf-8");

  const stats = await fs.stat(OUTPUT);
  console.log(`✅ Generated ${OUTPUT}`);
  console.log(`📦 Size: ${(stats.size / 1024).toFixed(1)}KB`);
  console.log(`🌍 Languages: ${langCodes.join(", ")}`);
  console.log(`🔑 Keys: ${allKeys.length}`);
  console.log(`⚡ Mode: Hyper-optimized`);
}

main().catch((err) => {
  console.error("❌ Error:", err);
});
