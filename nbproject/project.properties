abilities=ColorScreen,MIDP=2.0,ScreenHeight=320,JSR226=1.0,JSR184=1.1,JSR82=1.1,JSR280=1.0,OBEX=1.0,SATSA=1.0,WMA=2.0,ScreenColorDepth=16,JSR239=1.0,JSR234=1.0,JSR179=1.0,JSR256=1.2,JSR257=1.0,JSR177=1.0,JSR211=1.0,CLDC=1.1,J2MEWS=1.0,ScreenWidth=240,MMAPI=1.2,TouchScreen,JSR172=1.0,JSR75=1.0
all.configurations=\ 
application.args=
application.description=
application.description.detail=
application.name=
application.vendor=Vendor
build.classes.dir=${build.dir}/compiled
build.classes.excludes=**/*.java,**/*.form,**/*.class,**/.nbintdb,**/*.mvd,**/*.wsclient,**/*.vmd
build.dir=build/${config.active}
build.root.dir=build
debug.level=debug
debugger.timeout=
deployment.copy.target=deploy
deployment.instance=default
deployment.jarurl=${dist.jar}
deployment.method=NONE
deployment.override.jarurl=false
dist.dir=dist/${config.active}
dist.jad=MIDPlay_midlet.jad
dist.jar=MIDPlay_midlet.jar
dist.javadoc.dir=${dist.dir}/doc
dist.root.dir=dist
extra.classpath=
file.reference.MIDPlay-res=res
filter.exclude.tests=false
filter.excludes=
filter.more.excludes=**/overview.html,**/package.html
filter.use.standard=true
jar.compress=true
javac.debug=true
javac.deprecation=false
javac.encoding=UTF-8
javac.optimize=true
javac.source=1.3
javac.target=1.3
javadoc.author=false
javadoc.encoding=
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
libs.classpath=${file.reference.MIDPlay-res}
main.class=
main.class.class=applet
manifest.apipermissions=MIDlet-Permissions: javax.microedition.io.Connector.http\n
manifest.file=manifest.mf
manifest.is.liblet=false
manifest.jad=
manifest.manifest=
manifest.midlets=MIDlet-1: MIDPlay,/Icon.png,MIDPlay\n
manifest.others=MIDlet-Vendor: Duy Pham\nMIDlet-Version: 1.4.5\nNokia-UI-Enhancement: IgnoreProfilesBasedSoundMuting\nprogressive_download: enabled\nNokia-MIDlet-S60-Selection-Key-Compatibility: true\nMIDlet-Name: MIDPlay\nNokia-Scalable-Icon: /Icon.svg\nNokia-Scalable-Icon-MIDlet-1: /Icon.svg\n
manifest.pushregistry=
name=MIDPlay
no.dependencies=false
nokiaS80.application.icon=
obfuscated.classes.dir=${build.dir}/obfuscated
obfuscation.custom=
obfuscation.level=0
obfuscator.destjar=${build.dir}/obfuscated.jar
obfuscator.srcjar=${build.dir}/before-obfuscation.jar
platform.active=Oracle_Java_TM__Platform_Micro_Edition_SDK_3_4
platform.active.description=Oracle Java(TM) Platform Micro Edition SDK 3.4
platform.apis=JSR234-1.0,JSR257-1.0,JSR211-1.0,JSR75-1.0,J2ME-WS-1.0,JSR82-1.1,JSR239-1.0,JSR179-1.0,JSR184-1.1,MMAPI-1.2,JSR256-1.2,JSR226-1.0,SATSA-1.0,WMA-2.0,JSR280-1.0
platform.bootclasspath=${platform.home}/lib/jsr205_2.0.jar:${platform.home}/lib/jsr177_1.0.jar:${platform.home}/lib/jsr082_1.1.jar:${platform.home}/lib/jsr234_1.0.jar:${platform.home}/lib/jsr257_1.0.jar:${platform.home}/lib/jsr135_1.2.jar:${platform.home}/lib/jsr226_1.0.jar:${platform.home}/lib/jsr256_1.2.jar:${platform.home}/lib/jsr75_1.0.jar:${platform.home}/lib/jsr211_1.0.jar:${platform.home}/lib/jsr239_1.0.jar:${platform.home}/lib/jsr172_1.0.jar:${platform.home}/lib/jsr280_1.0.jar:${platform.home}/lib/jsr184_1.1.jar:${platform.home}/lib/jsr179_1.0.jar:${platform.home}/lib/cldc_1.1.jar:${platform.home}/lib/midp_2.0.jar
platform.configuration=CLDC-1.1
platform.device=JavaMEPhone1
platform.fat.jar=true
platform.profile=MIDP-2.0
platform.trigger=CLDC
platform.type=UEI-1.0.1
platform.java.home=C:/Program Files (x86)/Java/jdk1.8.0_112/bin/java.exe
platform.java.emulator=C:/kemnnx64/KEmulator.jar
preprocessed.dir=${build.dir}/preprocessed
preverify.classes.dir=${build.dir}/preverified
preverify.sources.dir=${build.dir}/preverifysrc
resources.dir=resources
run.cmd.options=
run.jvmargs=
run.method=STANDARD
run.security.domain=minimum
run.use.security.domain=false
savaje.application.icon=
savaje.application.icon.focused=
savaje.application.icon.small=
savaje.application.uid=TBD
savaje.bundle.base=
savaje.bundle.debug=false
savaje.bundle.debug.port=
semc.application.caps=
semc.application.icon=
semc.application.icon.count=
semc.application.icon.splash=
semc.application.icon.splash.installonly=false
semc.application.uid=********
semc.certificate.path=
semc.private.key.password=
semc.private.key.path=
sign.alias=minimal
sign.enabled=false
sign.keystore=
src.dir=src
use.emptyapis=true
use.preprocessor=true
